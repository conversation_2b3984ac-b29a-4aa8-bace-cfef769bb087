// Hover effects
@mixin hover-effect($bgcolor, $color: $light-color) {
  color: $color;
  background-color: $bgcolor;
  transition: $transition-base;
}

// Responsive breakpoints
@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$tablet}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$desktop}) {
    @content;
  }
}

@mixin large-desktop {
  @media (min-width: #{$large-desktop}) {
    @content;
  }
}

// Flexbox utilities
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// Button base
@mixin button-base {
  display: inline-block;
  padding: 12px 30px;
  border: none;
  border-radius: $border-radius;
  text-decoration: none;
  text-align: center;
  font-family: $main-font;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: $transition-base;

  &:focus {
    outline: none;
  }
}

// Card shadow
@mixin card-shadow {
  box-shadow: $box-shadow;
  transition: $transition-base;

  &:hover {
    box-shadow: $box-shadow-lg;
    transform: translateY(-5px);
  }
}

// Section padding
@mixin section-padding {
  padding: $spacer-xxl 0;

  @include mobile {
    padding: $spacer-xl 0;
  }
}

// Container
@mixin container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $container-padding;
}

// Overlay
@mixin overlay($color: rgba(0, 0, 0, 0.5)) {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $color;
    z-index: 1;
  }
}
