// Testimonials component
.testimonials {
    background-color: $gray-light;
    @include section-padding;

    &__container {
        @include container;
        text-align: center;
    }

    &__header {
        margin-bottom: $spacer-xl;

        h2 {
            margin-bottom: $spacer-md;
        }

        p {
            color: $font-color;
            font-size: $font-size-lg;
        }
    }

    &__slider {
        position: relative;
        overflow: hidden;
        max-width: 800px;
        margin: 0 auto;
    }

    &__track {
        display: flex;
        transition: transform 0.5s ease-in-out;
    }

    &__slide {
        min-width: 100%;
        padding: 0 $spacer-md;
    }
}

.testimonial-card {
    background-color: $light-color;
    padding: $spacer-xl;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow;
    position: relative;
    margin: $spacer-lg 0;

    &::before {
        content: '"';
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 4rem;
        color: $secondary-color;
        font-family: serif;
        line-height: 1;
        background-color: $light-color;
        padding: 0 $spacer-sm;
    }

    &__content {
        margin-bottom: $spacer-lg;
    }

    &__quote {
        font-size: $font-size-lg;
        line-height: 1.8;
        color: $font-color;
        font-style: italic;
        margin-bottom: $spacer-lg;
    }

    &__author {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: $spacer-md;

        @include mobile {
            flex-direction: column;
            gap: $spacer-sm;
        }
    }

    &__avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        border: 3px solid $secondary-color;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    &__info {
        text-align: left;

        @include mobile {
            text-align: center;
        }
    }

    &__name {
        font-size: $tiny-header;
        font-weight: 700;
        color: $dark-color;
        margin-bottom: $spacer-xs;
    }

    &__title {
        color: $font-color;
        font-size: $font-size-sm;
    }

    &__rating {
        display: flex;
        gap: $spacer-xs;
        margin-top: $spacer-sm;
        justify-content: center;

        @include mobile {
            justify-content: center;
        }

        i {
            color: $warning-color;
            font-size: 1rem;
        }
    }
}

// Testimonial navigation
.testimonials__nav {
    display: flex;
    justify-content: center;
    gap: $spacer-md;
    margin-top: $spacer-lg;

    &-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background-color: $light-color;
        border: 2px solid $border-color;
        border-radius: 50%;
        color: $font-color;
        cursor: pointer;
        transition: $transition-base;

        &:hover {
            background-color: $secondary-color;
            border-color: $secondary-color;
            color: $light-color;
        }

        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
                background-color: $light-color;
                border-color: $border-color;
                color: $font-color;
            }
        }

        i {
            font-size: 1.2rem;
        }
    }
}

// Testimonial dots
.testimonials__dots {
    display: flex;
    justify-content: center;
    gap: $spacer-sm;
    margin-top: $spacer-lg;

    &-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: $border-color;
        cursor: pointer;
        transition: $transition-base;

        &:hover {
            background-color: $secondary-color;
        }

        &.active {
            background-color: $secondary-color;
        }
    }
}

// Testimonial stats (optional section)
.testimonial-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacer-lg;
    margin-top: $spacer-xl;
    padding: $spacer-xl 0;
    border-top: 1px solid $border-color;

    &__item {
        text-align: center;
        padding: $spacer-lg;

        &-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: $secondary-color;
            margin-bottom: $spacer-sm;
            display: block;
        }

        &-label {
            color: $font-color;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
}

// Compact testimonial (for sidebar or smaller spaces)
.testimonial-compact {
    background-color: $light-color;
    padding: $spacer-lg;
    border-radius: $border-radius;
    border-left: 4px solid $secondary-color;
    margin-bottom: $spacer-lg;

    &__quote {
        font-size: $font-size-sm;
        line-height: 1.6;
        color: $font-color;
        font-style: italic;
        margin-bottom: $spacer-md;
    }

    &__author {
        font-weight: 600;
        color: $dark-color;
        font-size: $font-size-sm;

        &::before {
            content: '— ';
            color: $secondary-color;
        }
    }
}
