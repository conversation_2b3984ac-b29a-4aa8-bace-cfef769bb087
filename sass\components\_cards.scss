// Icon cards component (for departments)
.card-icon {
  display: flex;
  gap: $spacer-lg;
  padding: $spacer-lg;
  background-color: $light-color;
  border-radius: $border-radius-lg;
  @include card-shadow;
  transition: $transition-base;

  @include mobile {
    flex-direction: column;
    text-align: center;
    gap: $spacer-md;
  }

  &:hover {
    transform: translateY(-5px);

    .card-icon__img img {
      transform: scale(1.1);
    }
  }

  &__img {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    @include flex-center;

    @include mobile {
      width: 60px;
      height: 60px;
      margin: 0 auto;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      transition: $transition-base;
    }
  }

  &__text {
    flex: 1;

    h3 {
      color: $secondary-color;
      text-transform: capitalize;
      font-size: $small-header;
      margin-bottom: $spacer-md;
      font-weight: 700;

      a {
        color: inherit;
        transition: $transition-base;

        &:hover {
          color: $primary-color;
        }
      }
    }

    p {
      color: $font-color;
      line-height: 1.6;
      margin-bottom: $spacer-md;
    }

    .card-icon__link {
      display: inline-flex;
      align-items: center;
      gap: $spacer-xs;
      color: $secondary-color;
      font-weight: 600;
      font-size: $font-size-sm;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: $transition-base;

      &:hover {
        color: $primary-color;
        transform: translateX(3px);
      }

      i {
        font-size: 0.9rem;
      }
    }
  }
}

// General card component
.card {
  background-color: $light-color;
  border-radius: $border-radius-lg;
  overflow: hidden;
  @include card-shadow;

  &__header {
    padding: $spacer-lg;
    border-bottom: 1px solid $border-color;

    &-title {
      font-size: $small-header;
      font-weight: 700;
      color: $dark-color;
      margin-bottom: $spacer-xs;
    }

    &-subtitle {
      color: $font-color;
      font-size: $font-size-sm;
    }
  }

  &__body {
    padding: $spacer-lg;

    p:last-child {
      margin-bottom: 0;
    }
  }

  &__footer {
    padding: $spacer-lg;
    border-top: 1px solid $border-color;
    background-color: $gray-light;

    &-actions {
      display: flex;
      gap: $spacer-md;
      justify-content: flex-end;

      @include mobile {
        flex-direction: column;
      }
    }
  }

  &__image {
    width: 100%;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: $transition-base;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  // Card variants
  &--horizontal {
    display: flex;

    @include mobile {
      flex-direction: column;
    }

    .card__image {
      width: 200px;
      height: auto;

      @include mobile {
        width: 100%;
        height: 200px;
      }
    }

    .card__content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  &--featured {
    border: 2px solid $secondary-color;

    .card__header {
      background-color: $secondary-color;
      color: $light-color;
      border-bottom: none;

      .card__header-title {
        color: $light-color;
      }

      .card__header-subtitle {
        color: rgba($light-color, 0.9);
      }
    }
  }

  &--outline {
    border: 2px solid $border-color;
    box-shadow: none;

    &:hover {
      border-color: $secondary-color;
      box-shadow: $box-shadow;
    }
  }
}

// Card grid layout
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacer-xl;
  margin-top: $spacer-xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacer-lg;
  }

  &--2-cols {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));

    @include mobile {
      grid-template-columns: 1fr;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &--3-cols {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

    @include mobile {
      grid-template-columns: 1fr;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &--4-cols {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

    @include mobile {
      grid-template-columns: 1fr;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }

    @include large-desktop {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}
