// Footer styles
.footer {
    background-color: $dark-color;
    color: $light-color;
    padding: $spacer-xxl 0 $spacer-lg;

    &__main {
        margin-bottom: $spacer-xl;

        .container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: $spacer-xl;

            @include mobile {
                grid-template-columns: 1fr;
                gap: $spacer-lg;
            }
        }
    }

    &__section {
        &-title {
            font-size: $small-header;
            font-weight: 700;
            margin-bottom: $spacer-lg;
            color: $light-color;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: -8px;
                left: 0;
                width: 40px;
                height: 2px;
                background-color: $secondary-color;
            }
        }

        &-content {
            line-height: 1.8;

            p {
                margin-bottom: $spacer-md;
                color: rgba($light-color, 0.8);
            }

            ul {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    margin-bottom: $spacer-sm;
                    color: rgba($light-color, 0.8);

                    &:last-child {
                        margin-bottom: 0;
                    }

                    a {
                        color: rgba($light-color, 0.8);
                        transition: $transition-base;

                        &:hover {
                            color: $secondary-color;
                        }
                    }

                    i {
                        margin-right: $spacer-sm;
                        color: $secondary-color;
                        width: 16px;
                    }
                }
            }
        }
    }

    &__contact {
        &-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: $spacer-md;
            gap: $spacer-sm;

            &:last-child {
                margin-bottom: 0;
            }

            i {
                color: $secondary-color;
                font-size: 1.1rem;
                margin-top: 2px;
                min-width: 20px;
            }

            div {
                flex: 1;

                strong {
                    display: block;
                    color: $light-color;
                    margin-bottom: $spacer-xs;
                }

                span, a {
                    color: rgba($light-color, 0.8);
                    font-size: $font-size-sm;
                }

                a {
                    &:hover {
                        color: $secondary-color;
                    }
                }
            }
        }
    }

    &__hours {
        &-item {
            @include flex-between;
            padding: $spacer-sm 0;
            border-bottom: 1px solid rgba($light-color, 0.1);

            &:last-child {
                border-bottom: none;
            }

            span:first-child {
                color: $light-color;
                font-weight: 600;
            }

            span:last-child {
                color: rgba($light-color, 0.8);
            }
        }
    }

    &__emergency {
        background-color: $secondary-color;
        padding: $spacer-lg;
        border-radius: $border-radius-lg;
        text-align: center;

        &-title {
            font-size: $tiny-header;
            margin-bottom: $spacer-sm;
            color: $light-color;
        }

        &-phone {
            font-size: $medium-header;
            font-weight: 700;
            color: $light-color;
            text-decoration: none;

            &:hover {
                color: rgba($light-color, 0.9);
            }
        }
    }

    &__social {
        display: flex;
        gap: $spacer-md;
        margin-top: $spacer-lg;

        &-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: rgba($light-color, 0.1);
            border-radius: 50%;
            color: rgba($light-color, 0.8);
            transition: $transition-base;

            &:hover {
                background-color: $secondary-color;
                color: $light-color;
                transform: translateY(-2px);
            }

            i {
                font-size: 1.1rem;
            }
        }
    }

    &__bottom {
        border-top: 1px solid rgba($light-color, 0.1);
        padding-top: $spacer-lg;
        text-align: center;

        .container {
            @include flex-between;

            @include mobile {
                flex-direction: column;
                gap: $spacer-md;
                text-align: center;
            }
        }

        &-text {
            color: rgba($light-color, 0.6);
            font-size: $font-size-sm;
        }

        &-links {
            display: flex;
            gap: $spacer-lg;

            @include mobile {
                justify-content: center;
            }

            a {
                color: rgba($light-color, 0.6);
                font-size: $font-size-sm;
                transition: $transition-base;

                &:hover {
                    color: $secondary-color;
                }
            }
        }
    }
}
