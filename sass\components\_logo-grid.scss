// Logo grid component (for insurance providers, partners, etc.)
.logo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: $spacer-lg;
    align-items: center;
    margin-top: $spacer-xl;

    @include mobile {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacer-md;
    }

    @include tablet {
        grid-template-columns: repeat(3, 1fr);
    }

    @include desktop {
        grid-template-columns: repeat(4, 1fr);
    }

    @include large-desktop {
        grid-template-columns: repeat(6, 1fr);
    }

    &--insurance {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        
        @include mobile {
            grid-template-columns: repeat(2, 1fr);
        }

        @include tablet {
            grid-template-columns: repeat(3, 1fr);
        }

        @include desktop {
            grid-template-columns: repeat(4, 1fr);
        }

        @include large-desktop {
            grid-template-columns: repeat(6, 1fr);
        }
    }

    &--partners {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        
        @include mobile {
            grid-template-columns: repeat(1, 1fr);
        }

        @include tablet {
            grid-template-columns: repeat(2, 1fr);
        }

        @include desktop {
            grid-template-columns: repeat(3, 1fr);
        }

        @include large-desktop {
            grid-template-columns: repeat(4, 1fr);
        }
    }
}

.logo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacer-lg;
    background-color: $light-color;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    transition: $transition-base;
    height: 100px;

    &:hover {
        box-shadow: $box-shadow;
        transform: translateY(-2px);
        border-color: $secondary-color;
    }

    img {
        max-width: 100%;
        max-height: 60px;
        width: auto;
        height: auto;
        object-fit: contain;
        filter: grayscale(100%) opacity(0.7);
        transition: $transition-base;
    }

    &:hover img {
        filter: grayscale(0%) opacity(1);
    }

    // Insurance specific styling
    &--insurance {
        height: 80px;
        background-color: $gray-light;
        border: none;

        img {
            max-height: 40px;
            filter: grayscale(100%) opacity(0.6);
        }

        &:hover {
            background-color: $light-color;
            
            img {
                filter: grayscale(0%) opacity(1);
            }
        }
    }

    // Partner specific styling
    &--partner {
        height: 120px;
        
        img {
            max-height: 80px;
        }
    }
}

// Logo carousel (alternative layout)
.logo-carousel {
    position: relative;
    overflow: hidden;
    margin-top: $spacer-xl;

    &__track {
        display: flex;
        animation: logoScroll 30s linear infinite;
        gap: $spacer-xl;

        &:hover {
            animation-play-state: paused;
        }
    }

    &__item {
        flex-shrink: 0;
        width: 150px;
        height: 80px;
        @include flex-center;
        background-color: $light-color;
        border-radius: $border-radius;
        border: 1px solid $border-color;

        img {
            max-width: 80%;
            max-height: 60%;
            object-fit: contain;
            filter: grayscale(100%) opacity(0.7);
            transition: $transition-base;
        }

        &:hover img {
            filter: grayscale(0%) opacity(1);
        }
    }
}

@keyframes logoScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

// Logo section wrapper
.logo-section {
    background-color: $gray-light;
    @include section-padding;

    &__header {
        text-align: center;
        margin-bottom: $spacer-xl;

        h2 {
            margin-bottom: $spacer-md;
        }

        p {
            color: $font-color;
            font-size: $font-size-lg;
            max-width: 600px;
            margin: 0 auto;
        }
    }

    &--insurance {
        background-color: $light-color;
        padding: $spacer-xl 0;

        .logo-section__header h2 {
            font-size: $small-header;
            color: $font-color;
        }
    }

    &--partners {
        background-color: $gray-light;
    }
}

// Responsive adjustments
@include mobile {
    .logo-grid {
        gap: $spacer-sm;
    }

    .logo-item {
        padding: $spacer-md;
        height: 80px;

        img {
            max-height: 40px;
        }

        &--insurance {
            height: 60px;
            
            img {
                max-height: 30px;
            }
        }

        &--partner {
            height: 100px;
            
            img {
                max-height: 60px;
            }
        }
    }

    .logo-carousel__item {
        width: 120px;
        height: 60px;
    }
}

// Logo with text variant
.logo-text-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: $spacer-lg;
    background-color: $light-color;
    border-radius: $border-radius-lg;
    @include card-shadow;
    transition: $transition-base;

    &:hover {
        transform: translateY(-5px);
    }

    &__logo {
        width: 80px;
        height: 80px;
        @include flex-center;
        background-color: $gray-light;
        border-radius: 50%;
        margin-bottom: $spacer-md;

        img {
            max-width: 60%;
            max-height: 60%;
            object-fit: contain;
        }
    }

    &__name {
        font-weight: 700;
        color: $dark-color;
        margin-bottom: $spacer-xs;
    }

    &__description {
        color: $font-color;
        font-size: $font-size-sm;
        line-height: 1.5;
    }
}
