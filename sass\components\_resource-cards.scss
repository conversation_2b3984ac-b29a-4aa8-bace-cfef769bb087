// Resource cards component
.resource-card {
    background-color: $light-color;
    border-radius: $border-radius-lg;
    overflow: hidden;
    @include card-shadow;
    transition: $transition-base;

    &:hover {
        transform: translateY(-5px);
        
        .resource-card__image img {
            transform: scale(1.05);
        }
    }

    &__image {
        position: relative;
        overflow: hidden;
        height: 200px;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: $transition-base;
        }

        &-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                to bottom,
                transparent 0%,
                rgba($dark-color, 0.3) 100%
            );
        }
    }

    &__content {
        padding: $spacer-lg;
    }

    &__category {
        display: inline-block;
        background-color: $secondary-color;
        color: $light-color;
        padding: $spacer-xs $spacer-sm;
        border-radius: $border-radius;
        font-size: $font-size-sm;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: $spacer-md;
    }

    &__title {
        font-size: $small-header;
        font-weight: 700;
        color: $dark-color;
        margin-bottom: $spacer-md;
        line-height: 1.3;

        a {
            color: inherit;
            transition: $transition-base;

            &:hover {
                color: $secondary-color;
            }
        }
    }

    &__excerpt {
        color: $font-color;
        font-size: $font-size-sm;
        line-height: 1.6;
        margin-bottom: $spacer-lg;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    &__meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: $spacer-md;
        border-top: 1px solid $border-color;
        font-size: $font-size-sm;
        color: $font-color;

        @include mobile {
            flex-direction: column;
            gap: $spacer-sm;
            align-items: flex-start;
        }
    }

    &__date {
        display: flex;
        align-items: center;
        gap: $spacer-xs;

        i {
            color: $secondary-color;
        }
    }

    &__read-more {
        display: inline-flex;
        align-items: center;
        gap: $spacer-xs;
        color: $secondary-color;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: $transition-base;

        &:hover {
            color: $primary-color;
            transform: translateX(3px);
        }

        i {
            font-size: 0.9rem;
        }
    }
}

// Resource grid layout
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacer-xl;
    margin-top: $spacer-xl;

    @include mobile {
        grid-template-columns: 1fr;
        gap: $spacer-lg;
    }

    @include tablet {
        grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
        grid-template-columns: repeat(3, 1fr);
    }
}

// Featured resource card (larger)
.resource-card--featured {
    grid-column: span 2;

    @include mobile {
        grid-column: span 1;
    }

    .resource-card__image {
        height: 250px;
    }

    .resource-card__title {
        font-size: $medium-header;
    }

    .resource-card__content {
        padding: $spacer-xl;
    }
}

// Resource list view
.resource-list {
    .resource-card {
        display: flex;
        margin-bottom: $spacer-lg;

        @include mobile {
            flex-direction: column;
        }

        &__image {
            width: 200px;
            height: 150px;
            flex-shrink: 0;

            @include mobile {
                width: 100%;
                height: 200px;
            }
        }

        &__content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        &__excerpt {
            -webkit-line-clamp: 2;
        }
    }
}

// Resource filters
.resource-filters {
    display: flex;
    gap: $spacer-md;
    margin-bottom: $spacer-xl;
    flex-wrap: wrap;

    @include mobile {
        justify-content: center;
    }

    &__item {
        padding: $spacer-sm $spacer-lg;
        background-color: $gray-light;
        border: 2px solid transparent;
        border-radius: $border-radius;
        color: $font-color;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        cursor: pointer;
        transition: $transition-base;

        &:hover {
            background-color: $gray-medium;
            color: $dark-color;
        }

        &.active {
            background-color: $secondary-color;
            border-color: $secondary-color;
            color: $light-color;
        }
    }
}

// Resource pagination
.resource-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: $spacer-sm;
    margin-top: $spacer-xl;

    &__item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: 1px solid $border-color;
        border-radius: $border-radius;
        color: $font-color;
        font-weight: 600;
        transition: $transition-base;
        cursor: pointer;

        &:hover {
            background-color: $secondary-color;
            border-color: $secondary-color;
            color: $light-color;
        }

        &.active {
            background-color: $secondary-color;
            border-color: $secondary-color;
            color: $light-color;
        }

        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
                background-color: transparent;
                border-color: $border-color;
                color: $font-color;
            }
        }
    }
}
