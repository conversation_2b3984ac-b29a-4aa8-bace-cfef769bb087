// Doctor cards component
.doctor-card {
    background-color: $light-color;
    border-radius: $border-radius-lg;
    overflow: hidden;
    @include card-shadow;
    transition: $transition-base;

    &:hover {
        transform: translateY(-10px);
        
        .doctor-card__image img {
            transform: scale(1.05);
        }

        .doctor-card__overlay {
            opacity: 1;
        }
    }

    &__image {
        position: relative;
        overflow: hidden;
        height: 300px;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: $transition-base;
        }
    }

    &__overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
            45deg,
            rgba($primary-color, 0.9),
            rgba($secondary-color, 0.8)
        );
        @include flex-center;
        opacity: 0;
        transition: $transition-base;
    }

    &__social {
        display: flex;
        gap: $spacer-md;

        &-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background-color: rgba($light-color, 0.2);
            border: 2px solid rgba($light-color, 0.3);
            border-radius: 50%;
            color: $light-color;
            transition: $transition-base;

            &:hover {
                background-color: $light-color;
                color: $secondary-color;
                transform: scale(1.1);
            }

            i {
                font-size: 1.1rem;
            }
        }
    }

    &__content {
        padding: $spacer-lg;
        text-align: center;
    }

    &__specialty {
        display: inline-block;
        background-color: $secondary-color;
        color: $light-color;
        padding: $spacer-xs $spacer-md;
        border-radius: $border-radius;
        font-size: $font-size-sm;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: $spacer-md;
    }

    &__name {
        font-size: $small-header;
        font-weight: 700;
        color: $dark-color;
        margin-bottom: $spacer-sm;

        a {
            color: inherit;
            transition: $transition-base;

            &:hover {
                color: $secondary-color;
            }
        }
    }

    &__title {
        color: $font-color;
        font-size: $font-size-sm;
        margin-bottom: $spacer-md;
        font-style: italic;
    }

    &__bio {
        color: $font-color;
        font-size: $font-size-sm;
        line-height: 1.6;
        margin-bottom: $spacer-lg;
    }

    &__action {
        display: inline-flex;
        align-items: center;
        gap: $spacer-sm;
        color: $secondary-color;
        font-weight: 600;
        font-size: $font-size-sm;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: $transition-base;

        &:hover {
            color: $primary-color;
            transform: translateX(5px);
        }

        i {
            font-size: 1rem;
        }
    }
}

// Doctor grid layout
.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: $spacer-xl;
    margin-top: $spacer-xl;

    @include mobile {
        grid-template-columns: 1fr;
        gap: $spacer-lg;
    }

    @include tablet {
        grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
        grid-template-columns: repeat(3, 1fr);
    }

    @include large-desktop {
        grid-template-columns: repeat(4, 1fr);
    }
}

// Doctor info card (for detailed pages)
.doctor-info {
    background-color: $light-color;
    border-radius: $border-radius-lg;
    padding: $spacer-xl;
    box-shadow: $box-shadow;

    &__header {
        display: flex;
        gap: $spacer-lg;
        margin-bottom: $spacer-xl;

        @include mobile {
            flex-direction: column;
            text-align: center;
        }
    }

    &__image {
        flex-shrink: 0;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        overflow: hidden;

        @include mobile {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    &__details {
        flex: 1;

        h3 {
            color: $dark-color;
            margin-bottom: $spacer-sm;
        }

        .specialty {
            color: $secondary-color;
            font-weight: 600;
            margin-bottom: $spacer-md;
        }

        .qualifications {
            color: $font-color;
            font-size: $font-size-sm;
            margin-bottom: $spacer-md;
        }

        .contact {
            display: flex;
            gap: $spacer-lg;
            margin-top: $spacer-md;

            @include mobile {
                flex-direction: column;
                gap: $spacer-sm;
            }

            &-item {
                display: flex;
                align-items: center;
                gap: $spacer-sm;
                color: $font-color;
                font-size: $font-size-sm;

                i {
                    color: $secondary-color;
                }
            }
        }
    }

    &__bio {
        color: $font-color;
        line-height: 1.8;
        margin-bottom: $spacer-lg;
    }

    &__specialties {
        h4 {
            color: $dark-color;
            margin-bottom: $spacer-md;
        }

        ul {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: $spacer-sm;

            li {
                padding: $spacer-sm;
                background-color: $gray-light;
                border-radius: $border-radius;
                color: $font-color;
                font-size: $font-size-sm;

                &::before {
                    content: '✓';
                    color: $secondary-color;
                    font-weight: bold;
                    margin-right: $spacer-sm;
                }
            }
        }
    }
}
