// Button component
.btn {
  @include button-base;
  display: inline-flex;
  align-items: center;
  gap: $spacer-sm;
  font-size: $font-size-sm;
  border: 2px solid transparent;

  // Primary button
  &--primary {
    background-color: $secondary-color;
    color: $light-color;
    border-color: $secondary-color;

    &:hover {
      background-color: $primary-color;
      border-color: $primary-color;
      transform: translateY(-2px);
    }
  }

  // Secondary button
  &--secondary {
    background-color: transparent;
    color: $secondary-color;
    border-color: $secondary-color;

    &:hover {
      @include hover-effect($secondary-color);
      transform: translateY(-2px);
    }
  }

  // Dark button
  &--dark {
    background-color: transparent;
    color: $dark-color;
    border-color: $dark-color;

    &:hover {
      @include hover-effect($dark-color);
      transform: translateY(-2px);
    }
  }

  // Light button
  &--light {
    background-color: $light-color;
    color: $dark-color;
    border-color: $light-color;

    &:hover {
      background-color: transparent;
      color: $light-color;
      border-color: $light-color;
    }
  }

  // Success button
  &--success {
    background-color: $success-color;
    color: $light-color;
    border-color: $success-color;

    &:hover {
      background-color: darken($success-color, 10%);
      border-color: darken($success-color, 10%);
      transform: translateY(-2px);
    }
  }

  // Warning button
  &--warning {
    background-color: $warning-color;
    color: $dark-color;
    border-color: $warning-color;

    &:hover {
      background-color: darken($warning-color, 10%);
      border-color: darken($warning-color, 10%);
      transform: translateY(-2px);
    }
  }

  // Danger button
  &--danger {
    background-color: $danger-color;
    color: $light-color;
    border-color: $danger-color;

    &:hover {
      background-color: darken($danger-color, 10%);
      border-color: darken($danger-color, 10%);
      transform: translateY(-2px);
    }
  }

  // Button sizes
  &--sm {
    padding: 8px 20px;
    font-size: 0.75rem;
  }

  &--lg {
    padding: 15px 40px;
    font-size: $font-size-base;
  }

  &--xl {
    padding: 18px 50px;
    font-size: $font-size-lg;
  }

  // Button shapes
  &--rounded {
    border-radius: $border-radius-xl;
  }

  &--pill {
    border-radius: 50px;
  }

  // Block button
  &--block {
    width: 100%;
    justify-content: center;
  }

  // Icon button
  &--icon-only {
    width: 45px;
    height: 45px;
    padding: 0;
    justify-content: center;
    border-radius: 50%;

    &.btn--sm {
      width: 35px;
      height: 35px;
    }

    &.btn--lg {
      width: 55px;
      height: 55px;
    }
  }

  // Disabled state
  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;

    &:hover {
      transform: none !important;
    }
  }

  // Loading state
  &.loading {
    position: relative;
    color: transparent !important;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid currentColor;
      border-top-color: transparent;
      border-radius: 50%;
      animation: btnSpin 0.8s linear infinite;
    }
  }
}

@keyframes btnSpin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// Button group
.btn-group {
  display: inline-flex;
  border-radius: $border-radius;
  overflow: hidden;

  .btn {
    border-radius: 0;
    border-right-width: 1px;

    &:first-child {
      border-top-left-radius: $border-radius;
      border-bottom-left-radius: $border-radius;
    }

    &:last-child {
      border-top-right-radius: $border-radius;
      border-bottom-right-radius: $border-radius;
      border-right-width: 2px;
    }

    &:not(:last-child) {
      border-right-color: rgba($light-color, 0.2);
    }
  }
}
