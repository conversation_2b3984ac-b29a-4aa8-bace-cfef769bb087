* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.container {
  width: 80%;
  margin: 0 auto;
}

.depratment {
  background-color: #f7f7f7;
  padding: 50px 0;
}
.depratment__card-row {
  display: flex;
  margin: 50px 0;
  justify-content: space-evenly;
}
.depratment__card-row .card-icon {
  width: 30%;
}
.depratment__btn {
  display: flex;
  justify-content: center;
}

.btn {
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 700;
  font-family: sans-serif;
  font-size: 12px;
  padding: 10px 20px;
  background-color: transparent;
}
.btn--primary {
  color: #008fe2;
}
.btn--primary:hover {
  color: #ffffff;
  background-color: #2d529f;
}
.btn--secondry {
  color: #ffffff;
  background-color: #008fe2;
}
.btn--dark {
  color: black;
  border: 2px solid black;
}
.btn--dark:hover {
  color: #ffffff;
  background-color: black;
}

.sec-title__header {
  color: black;
  font-family: sans-serif;
  font-size: 1.7rem;
  margin-bottom: 10px;
  font-weight: 700;
  text-transform: capitalize;
}
.sec-title p {
  color: #777;
}

.card-icon {
  display: flex;
  gap: 1.2rem;
}
.card-icon__text h3 {
  color: #008fe2;
  text-transform: capitalize;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
.card-icon__text p {
  color: #777;
}/*# sourceMappingURL=main.css.map */