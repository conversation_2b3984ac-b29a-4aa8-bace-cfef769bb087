@charset "UTF-8";
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Open Sans", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #777777;
  background-color: #ffffff;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Montserrat", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: #333333;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 1.7rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  margin-bottom: 1rem;
}
p:last-child {
  margin-bottom: 0;
}

a {
  color: #0088cc;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}
a:hover {
  color: #0088cc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section {
  padding: 4rem 0;
}
@media (max-width: 575px) {
  .section {
    padding: 3rem 0;
  }
}

.top-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 10px 0;
  font-size: 0.875rem;
}
.top-header__content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 575px) {
  .top-header__content {
    justify-content: center;
  }
}
.top-header__info {
  display: flex;
  gap: 3rem;
}
@media (max-width: 575px) {
  .top-header__info {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}
@media (min-width: 768px) {
  .top-header__info {
    gap: 1.5rem;
  }
}
.top-header__item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #777777;
}
.top-header__item i {
  color: #0088cc;
  font-size: 0.875rem;
}
.top-header__item span {
  font-weight: 400;
}
@media (max-width: 575px) {
  .top-header__item {
    justify-content: center;
  }
}

.header {
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 1000;
}
.header__main {
  padding: 15px 0;
}
.header__main .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: center;
}
.header__logo img {
  max-height: 50px;
  width: auto;
}
.header__nav {
  display: flex;
  align-items: center;
}
@media (max-width: 575px) {
  .header__nav {
    display: none;
  }
}
.header__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 30px;
}
.header__nav-item {
  position: relative;
}
.header__nav-item:hover .header__nav-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.header__nav-link {
  color: #333333;
  font-weight: 400;
  font-size: 14px;
  padding: 10px 0;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
}
.header__nav-link:hover {
  color: #0088cc;
}
.header__nav-link--active {
  color: #0088cc;
}
.header__nav-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #ffffff;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  border-radius: 0.25rem;
  padding: 1rem 0;
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease-in-out;
  z-index: 1000;
}
.header__nav-dropdown-item {
  padding: 0.5rem 1rem;
}
.header__nav-dropdown-item:hover {
  background-color: #f8f9fa;
}
.header__nav-dropdown-link {
  color: #333333;
  font-weight: 400;
  text-transform: none;
  letter-spacing: normal;
}
.header__nav-dropdown-link:hover {
  color: #0088cc;
}
.header__toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #333333;
  cursor: pointer;
}
@media (max-width: 575px) {
  .header__toggle {
    display: block;
  }
}
.header__mobile-menu {
  display: none;
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  padding: 1rem 0;
}
@media (max-width: 575px) {
  .header__mobile-menu.active {
    display: block;
  }
}
.header__mobile-menu .header__nav-list {
  flex-direction: column;
  gap: 0;
}
.header__mobile-menu .header__nav-item {
  border-bottom: 1px solid #e5e5e5;
}
.header__mobile-menu .header__nav-item:last-child {
  border-bottom: none;
}
.header__mobile-menu .header__nav-link {
  display: block;
  padding: 1rem;
  text-transform: none;
}

.hero {
  position: relative;
  height: 500px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hero__slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.hero__slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}
.hero__slide.active {
  opacity: 1;
}
.hero__slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(0, 136, 204, 0.8), rgba(0, 136, 204, 0.6));
  z-index: 1;
}
.hero__content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: #ffffff;
  max-width: 800px;
  padding: 0 1rem;
}
@media (max-width: 575px) {
  .hero__content {
    max-width: 100%;
  }
}
.hero__subtitle {
  font-size: 1.125rem;
  font-weight: 400;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  opacity: 0.9;
}
@media (max-width: 575px) {
  .hero__subtitle {
    font-size: 1rem;
  }
}
.hero__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}
@media (max-width: 575px) {
  .hero__title {
    font-size: 2rem;
  }
}
@media (min-width: 768px) {
  .hero__title {
    font-size: 2.2rem;
  }
}
.hero__description {
  font-size: 1.125rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  line-height: 1.6;
}
@media (max-width: 575px) {
  .hero__description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
}
.hero__actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}
@media (max-width: 575px) {
  .hero__actions {
    flex-direction: column;
    align-items: center;
  }
}
.hero__nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.hero__nav:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}
.hero__nav--prev {
  left: 1.5rem;
}
.hero__nav--next {
  right: 1.5rem;
}
@media (max-width: 575px) {
  .hero__nav {
    display: none;
  }
}
.hero__dots {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 3;
}
.hero__dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.hero__dot.active {
  background-color: #ffffff;
}
.hero__dot:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.hero-cards {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1.5rem;
  z-index: 4;
}
@media (max-width: 575px) {
  .hero-cards {
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    flex-direction: column;
    margin-top: 1.5rem;
    padding: 0 1rem;
  }
}
@media (min-width: 768px) {
  .hero-cards {
    gap: 1rem;
  }
}

.hero-card {
  background-color: #ffffff;
  padding: 3rem 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  text-align: center;
  min-width: 250px;
  transition: all 0.3s ease-in-out;
}
@media (max-width: 575px) {
  .hero-card {
    min-width: auto;
    padding: 1.5rem;
  }
}
.hero-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2);
}
.hero-card__icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0088cc;
  border-radius: 50%;
  color: #ffffff;
  font-size: 1.5rem;
}
.hero-card__title {
  font-size: 1.5rem;
  color: #333333;
  margin-bottom: 0.5rem;
  font-weight: 700;
}
.hero-card__subtitle {
  color: #777777;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}
.hero-card__info {
  font-size: 0.875rem;
  color: #777777;
  line-height: 1.5;
}
.hero-card__info strong {
  color: #333333;
  display: block;
  margin-bottom: 0.25rem;
}

.department, .depratment {
  background-color: #f5f5f5;
  padding: 4rem 0;
}
@media (max-width: 575px) {
  .department, .depratment {
    padding: 3rem 0;
  }
}
.department__header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.department__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}
@media (max-width: 575px) {
  .department__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .department__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .department__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
.department__actions {
  text-align: center;
  margin-top: 3rem;
}

.depratment__card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin: 3rem 0;
}
@media (max-width: 575px) {
  .depratment__card-row {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .depratment__card-row {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .depratment__card-row {
    grid-template-columns: repeat(3, 1fr);
  }
}
.depratment__card-row .card-icon {
  width: 100%;
}
.depratment__btn {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.about {
  padding: 4rem 0;
  background-color: #ffffff;
}
@media (max-width: 575px) {
  .about {
    padding: 3rem 0;
  }
}
.about__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}
@media (max-width: 575px) {
  .about__container {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}
.about__content h2 {
  margin-bottom: 1.5rem;
}
.about__content p {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  line-height: 1.8;
}
.about__content .btn {
  margin-top: 1.5rem;
}
.about__image {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}
.about__image img {
  width: 100%;
  height: auto;
  display: block;
}
.about__image::after {
  content: "";
  position: absolute;
  top: 20px;
  right: 20px;
  bottom: 20px;
  left: 20px;
  border: 3px solid #0088cc;
  border-radius: 0.5rem;
  pointer-events: none;
}

.doctors {
  padding: 4rem 0;
  background-color: #f8f9fa;
}
@media (max-width: 575px) {
  .doctors {
    padding: 3rem 0;
  }
}
.doctors__header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.doctors__actions {
  text-align: center;
  margin-top: 3rem;
}

.resources {
  padding: 4rem 0;
  background-color: #ffffff;
}
@media (max-width: 575px) {
  .resources {
    padding: 3rem 0;
  }
}
.resources__header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.resources__actions {
  text-align: center;
  margin-top: 3rem;
}

.insurance {
  padding: 4rem 0;
  background-color: #f8f9fa;
}
@media (max-width: 575px) {
  .insurance {
    padding: 3rem 0;
  }
}
.insurance__header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.footer {
  background-color: #333333;
  color: #ffffff;
  padding: 4rem 0 1.5rem;
}
.footer__main {
  margin-bottom: 3rem;
}
.footer__main .container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
}
@media (max-width: 575px) {
  .footer__main .container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
.footer__section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #ffffff;
  position: relative;
}
.footer__section-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: #0088cc;
}
.footer__section-content {
  line-height: 1.8;
}
.footer__section-content p {
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.8);
}
.footer__section-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.footer__section-content ul li {
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}
.footer__section-content ul li:last-child {
  margin-bottom: 0;
}
.footer__section-content ul li a {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease-in-out;
}
.footer__section-content ul li a:hover {
  color: #0088cc;
}
.footer__section-content ul li i {
  margin-right: 0.5rem;
  color: #0088cc;
  width: 16px;
}
.footer__contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 0.5rem;
}
.footer__contact-item:last-child {
  margin-bottom: 0;
}
.footer__contact-item i {
  color: #0088cc;
  font-size: 1.1rem;
  margin-top: 2px;
  min-width: 20px;
}
.footer__contact-item div {
  flex: 1;
}
.footer__contact-item div strong {
  display: block;
  color: #ffffff;
  margin-bottom: 0.25rem;
}
.footer__contact-item div span, .footer__contact-item div a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}
.footer__contact-item div a:hover {
  color: #0088cc;
}
.footer__hours-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.footer__hours-item:last-child {
  border-bottom: none;
}
.footer__hours-item span:first-child {
  color: #ffffff;
  font-weight: 600;
}
.footer__hours-item span:last-child {
  color: rgba(255, 255, 255, 0.8);
}
.footer__emergency {
  background-color: #0088cc;
  padding: 1.5rem;
  border-radius: 0.5rem;
  text-align: center;
}
.footer__emergency-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #ffffff;
}
.footer__emergency-phone {
  font-size: 1.7rem;
  font-weight: 700;
  color: #ffffff;
  text-decoration: none;
}
.footer__emergency-phone:hover {
  color: rgba(255, 255, 255, 0.9);
}
.footer__social {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}
.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease-in-out;
}
.footer__social-link:hover {
  background-color: #0088cc;
  color: #ffffff;
  transform: translateY(-2px);
}
.footer__social-link i {
  font-size: 1.1rem;
}
.footer__bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
  text-align: center;
}
.footer__bottom .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 575px) {
  .footer__bottom .container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}
.footer__bottom-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}
.footer__bottom-links {
  display: flex;
  gap: 1.5rem;
}
@media (max-width: 575px) {
  .footer__bottom-links {
    justify-content: center;
  }
}
.footer__bottom-links a {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  transition: all 0.3s ease-in-out;
}
.footer__bottom-links a:hover {
  color: #0088cc;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border: none;
  border-radius: 0.25rem;
  text-decoration: none;
  text-align: center;
  font-family: "Open Sans", sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  border: 2px solid transparent;
}
.btn:focus {
  outline: none;
}
.btn--primary {
  background-color: #0088cc;
  color: #ffffff;
  border-color: #0088cc;
}
.btn--primary:hover {
  background-color: #0088cc;
  border-color: #0088cc;
  transform: translateY(-2px);
}
.btn--secondary {
  background-color: transparent;
  color: #0088cc;
  border-color: #0088cc;
}
.btn--secondary:hover {
  color: #ffffff;
  background-color: #0088cc;
  transition: all 0.3s ease-in-out;
  transform: translateY(-2px);
}
.btn--dark {
  background-color: transparent;
  color: #333333;
  border-color: #333333;
}
.btn--dark:hover {
  color: #ffffff;
  background-color: #333333;
  transition: all 0.3s ease-in-out;
  transform: translateY(-2px);
}
.btn--light {
  background-color: #ffffff;
  color: #333333;
  border-color: #ffffff;
}
.btn--light:hover {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
}
.btn--success {
  background-color: #28a745;
  color: #ffffff;
  border-color: #28a745;
}
.btn--success:hover {
  background-color: rgb(30.1449275362, 125.8550724638, 52);
  border-color: rgb(30.1449275362, 125.8550724638, 52);
  transform: translateY(-2px);
}
.btn--warning {
  background-color: #ffc107;
  color: #333333;
  border-color: #ffc107;
}
.btn--warning:hover {
  background-color: rgb(211, 158.25, 0);
  border-color: rgb(211, 158.25, 0);
  transform: translateY(-2px);
}
.btn--danger {
  background-color: #dc3545;
  color: #ffffff;
  border-color: #dc3545;
}
.btn--danger:hover {
  background-color: rgb(189.2151898734, 32.7848101266, 47.7721518987);
  border-color: rgb(189.2151898734, 32.7848101266, 47.7721518987);
  transform: translateY(-2px);
}
.btn--sm {
  padding: 8px 20px;
  font-size: 0.75rem;
}
.btn--lg {
  padding: 15px 40px;
  font-size: 1rem;
}
.btn--xl {
  padding: 18px 50px;
  font-size: 1.125rem;
}
.btn--rounded {
  border-radius: 1rem;
}
.btn--pill {
  border-radius: 50px;
}
.btn--block {
  width: 100%;
  justify-content: center;
}
.btn--icon-only {
  width: 45px;
  height: 45px;
  padding: 0;
  justify-content: center;
  border-radius: 50%;
}
.btn--icon-only.btn--sm {
  width: 35px;
  height: 35px;
}
.btn--icon-only.btn--lg {
  width: 55px;
  height: 55px;
}
.btn:disabled, .btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}
.btn:disabled:hover, .btn.disabled:hover {
  transform: none !important;
}
.btn.loading {
  position: relative;
  color: transparent !important;
}
.btn.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: btnSpin 0.8s linear infinite;
}

@keyframes btnSpin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
.btn-group {
  display: inline-flex;
  border-radius: 0.25rem;
  overflow: hidden;
}
.btn-group .btn {
  border-radius: 0;
  border-right-width: 1px;
}
.btn-group .btn:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.btn-group .btn:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  border-right-width: 2px;
}
.btn-group .btn:not(:last-child) {
  border-right-color: rgba(255, 255, 255, 0.2);
}

.sec-title__header {
  color: #333333;
  font-family: "Open Sans", sans-serif;
  font-size: 1.7rem;
  margin-bottom: 10px;
  font-weight: 700;
  text-transform: capitalize;
}
.sec-title p {
  color: #777777;
}

.card-icon {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.card-icon:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
@media (max-width: 575px) {
  .card-icon {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}
.card-icon:hover {
  transform: translateY(-5px);
}
.card-icon:hover .card-icon__img img {
  transform: scale(1.1);
}
.card-icon__img {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 575px) {
  .card-icon__img {
    width: 60px;
    height: 60px;
    margin: 0 auto;
  }
}
.card-icon__img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 0.3s ease-in-out;
}
.card-icon__text {
  flex: 1;
}
.card-icon__text h3 {
  color: #0088cc;
  text-transform: capitalize;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}
.card-icon__text h3 a {
  color: inherit;
  transition: all 0.3s ease-in-out;
}
.card-icon__text h3 a:hover {
  color: #0088cc;
}
.card-icon__text p {
  color: #777777;
  line-height: 1.6;
  margin-bottom: 1rem;
}
.card-icon__text .card-icon__link {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  color: #0088cc;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease-in-out;
}
.card-icon__text .card-icon__link:hover {
  color: #0088cc;
  transform: translateX(3px);
}
.card-icon__text .card-icon__link i {
  font-size: 0.9rem;
}

.card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease-in-out;
}
.card:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.card__header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
}
.card__header-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.25rem;
}
.card__header-subtitle {
  color: #777777;
  font-size: 0.875rem;
}
.card__body {
  padding: 1.5rem;
}
.card__body p:last-child {
  margin-bottom: 0;
}
.card__footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e5e5;
  background-color: #f8f9fa;
}
.card__footer-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
@media (max-width: 575px) {
  .card__footer-actions {
    flex-direction: column;
  }
}
.card__image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}
.card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease-in-out;
}
.card__image:hover img {
  transform: scale(1.05);
}
.card--horizontal {
  display: flex;
}
@media (max-width: 575px) {
  .card--horizontal {
    flex-direction: column;
  }
}
.card--horizontal .card__image {
  width: 200px;
  height: auto;
}
@media (max-width: 575px) {
  .card--horizontal .card__image {
    width: 100%;
    height: 200px;
  }
}
.card--horizontal .card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.card--featured {
  border: 2px solid #0088cc;
}
.card--featured .card__header {
  background-color: #0088cc;
  color: #ffffff;
  border-bottom: none;
}
.card--featured .card__header .card__header-title {
  color: #ffffff;
}
.card--featured .card__header .card__header-subtitle {
  color: rgba(255, 255, 255, 0.9);
}
.card--outline {
  border: 2px solid #e5e5e5;
  box-shadow: none;
}
.card--outline:hover {
  border-color: #0088cc;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}
@media (max-width: 575px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
.cards-grid--2-cols {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}
@media (max-width: 575px) {
  .cards-grid--2-cols {
    grid-template-columns: 1fr;
  }
}
@media (min-width: 768px) {
  .cards-grid--2-cols {
    grid-template-columns: repeat(2, 1fr);
  }
}
.cards-grid--3-cols {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
@media (max-width: 575px) {
  .cards-grid--3-cols {
    grid-template-columns: 1fr;
  }
}
@media (min-width: 768px) {
  .cards-grid--3-cols {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .cards-grid--3-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}
.cards-grid--4-cols {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
@media (max-width: 575px) {
  .cards-grid--4-cols {
    grid-template-columns: 1fr;
  }
}
@media (min-width: 768px) {
  .cards-grid--4-cols {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .cards-grid--4-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1200px) {
  .cards-grid--4-cols {
    grid-template-columns: repeat(4, 1fr);
  }
}

.doctor-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.doctor-card:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.doctor-card:hover {
  transform: translateY(-10px);
}
.doctor-card:hover .doctor-card__image img {
  transform: scale(1.05);
}
.doctor-card:hover .doctor-card__overlay {
  opacity: 1;
}
.doctor-card__image {
  position: relative;
  overflow: hidden;
  height: 300px;
}
.doctor-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease-in-out;
}
.doctor-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 136, 204, 0.9), rgba(0, 136, 204, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}
.doctor-card__social {
  display: flex;
  gap: 1rem;
}
.doctor-card__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: #ffffff;
  transition: all 0.3s ease-in-out;
}
.doctor-card__social-link:hover {
  background-color: #ffffff;
  color: #0088cc;
  transform: scale(1.1);
}
.doctor-card__social-link i {
  font-size: 1.1rem;
}
.doctor-card__content {
  padding: 1.5rem;
  text-align: center;
}
.doctor-card__specialty {
  display: inline-block;
  background-color: #0088cc;
  color: #ffffff;
  padding: 0.25rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
}
.doctor-card__name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.5rem;
}
.doctor-card__name a {
  color: inherit;
  transition: all 0.3s ease-in-out;
}
.doctor-card__name a:hover {
  color: #0088cc;
}
.doctor-card__title {
  color: #777777;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  font-style: italic;
}
.doctor-card__bio {
  color: #777777;
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}
.doctor-card__action {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #0088cc;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease-in-out;
}
.doctor-card__action:hover {
  color: #0088cc;
  transform: translateX(5px);
}
.doctor-card__action i {
  font-size: 1rem;
}

.doctors-grid, .doctors__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}
@media (max-width: 575px) {
  .doctors-grid, .doctors__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .doctors-grid, .doctors__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .doctors-grid, .doctors__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1200px) {
  .doctors-grid, .doctors__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.doctor-info {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 3rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.doctor-info__header {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 3rem;
}
@media (max-width: 575px) {
  .doctor-info__header {
    flex-direction: column;
    text-align: center;
  }
}
.doctor-info__image {
  flex-shrink: 0;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
}
@media (max-width: 575px) {
  .doctor-info__image {
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }
}
.doctor-info__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.doctor-info__details {
  flex: 1;
}
.doctor-info__details h3 {
  color: #333333;
  margin-bottom: 0.5rem;
}
.doctor-info__details .specialty {
  color: #0088cc;
  font-weight: 600;
  margin-bottom: 1rem;
}
.doctor-info__details .qualifications {
  color: #777777;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}
.doctor-info__details .contact {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
}
@media (max-width: 575px) {
  .doctor-info__details .contact {
    flex-direction: column;
    gap: 0.5rem;
  }
}
.doctor-info__details .contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #777777;
  font-size: 0.875rem;
}
.doctor-info__details .contact-item i {
  color: #0088cc;
}
.doctor-info__bio {
  color: #777777;
  line-height: 1.8;
  margin-bottom: 1.5rem;
}
.doctor-info__specialties h4 {
  color: #333333;
  margin-bottom: 1rem;
}
.doctor-info__specialties ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}
.doctor-info__specialties ul li {
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  color: #777777;
  font-size: 0.875rem;
}
.doctor-info__specialties ul li::before {
  content: "✓";
  color: #0088cc;
  font-weight: bold;
  margin-right: 0.5rem;
}

.resource-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.resource-card:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.resource-card:hover {
  transform: translateY(-5px);
}
.resource-card:hover .resource-card__image img {
  transform: scale(1.05);
}
.resource-card__image {
  position: relative;
  overflow: hidden;
  height: 200px;
}
.resource-card__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease-in-out;
}
.resource-card__image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(51, 51, 51, 0.3) 100%);
}
.resource-card__content {
  padding: 1.5rem;
}
.resource-card__category {
  display: inline-block;
  background-color: #0088cc;
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
}
.resource-card__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 1rem;
  line-height: 1.3;
}
.resource-card__title a {
  color: inherit;
  transition: all 0.3s ease-in-out;
}
.resource-card__title a:hover {
  color: #0088cc;
}
.resource-card__excerpt {
  color: #777777;
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.resource-card__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 1rem;
  border-top: 1px solid #e5e5e5;
  font-size: 0.875rem;
  color: #777777;
}
@media (max-width: 575px) {
  .resource-card__meta {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}
.resource-card__date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.resource-card__date i {
  color: #0088cc;
}
.resource-card__read-more {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  color: #0088cc;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease-in-out;
}
.resource-card__read-more:hover {
  color: #0088cc;
  transform: translateX(3px);
}
.resource-card__read-more i {
  font-size: 0.9rem;
}

.resources-grid, .resources__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}
@media (max-width: 575px) {
  .resources-grid, .resources__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
@media (min-width: 768px) {
  .resources-grid, .resources__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .resources-grid, .resources__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.resource-card--featured {
  grid-column: span 2;
}
@media (max-width: 575px) {
  .resource-card--featured {
    grid-column: span 1;
  }
}
.resource-card--featured .resource-card__image {
  height: 250px;
}
.resource-card--featured .resource-card__title {
  font-size: 1.7rem;
}
.resource-card--featured .resource-card__content {
  padding: 3rem;
}

.resource-list .resource-card {
  display: flex;
  margin-bottom: 1.5rem;
}
@media (max-width: 575px) {
  .resource-list .resource-card {
    flex-direction: column;
  }
}
.resource-list .resource-card__image {
  width: 200px;
  height: 150px;
  flex-shrink: 0;
}
@media (max-width: 575px) {
  .resource-list .resource-card__image {
    width: 100%;
    height: 200px;
  }
}
.resource-list .resource-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.resource-list .resource-card__excerpt {
  -webkit-line-clamp: 2;
}

.resource-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}
@media (max-width: 575px) {
  .resource-filters {
    justify-content: center;
  }
}
.resource-filters__item {
  padding: 0.5rem 1.5rem;
  background-color: #f8f9fa;
  border: 2px solid transparent;
  border-radius: 0.25rem;
  color: #777777;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.resource-filters__item:hover {
  background-color: #e9ecef;
  color: #333333;
}
.resource-filters__item.active {
  background-color: #0088cc;
  border-color: #0088cc;
  color: #ffffff;
}

.resource-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 3rem;
}
.resource-pagination__item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #e5e5e5;
  border-radius: 0.25rem;
  color: #777777;
  font-weight: 600;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}
.resource-pagination__item:hover {
  background-color: #0088cc;
  border-color: #0088cc;
  color: #ffffff;
}
.resource-pagination__item.active {
  background-color: #0088cc;
  border-color: #0088cc;
  color: #ffffff;
}
.resource-pagination__item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.resource-pagination__item.disabled:hover {
  background-color: transparent;
  border-color: #e5e5e5;
  color: #777777;
}

.testimonials {
  background-color: #f8f9fa;
  padding: 4rem 0;
}
@media (max-width: 575px) {
  .testimonials {
    padding: 3rem 0;
  }
}
.testimonials__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  text-align: center;
}
.testimonials__header {
  margin-bottom: 3rem;
}
.testimonials__header h2 {
  margin-bottom: 1rem;
}
.testimonials__header p {
  color: #777777;
  font-size: 1.125rem;
}
.testimonials__slider {
  position: relative;
  overflow: hidden;
  max-width: 800px;
  margin: 0 auto;
}
.testimonials__track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}
.testimonials__slide {
  min-width: 100%;
  padding: 0 1rem;
}

.testimonial-card {
  background-color: #ffffff;
  padding: 3rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  position: relative;
  margin: 1.5rem 0;
}
.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 4rem;
  color: #0088cc;
  font-family: serif;
  line-height: 1;
  background-color: #ffffff;
  padding: 0 0.5rem;
}
.testimonial-card__content {
  margin-bottom: 1.5rem;
}
.testimonial-card__quote {
  font-size: 1.125rem;
  line-height: 1.8;
  color: #777777;
  font-style: italic;
  margin-bottom: 1.5rem;
}
.testimonial-card__author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}
@media (max-width: 575px) {
  .testimonial-card__author {
    flex-direction: column;
    gap: 0.5rem;
  }
}
.testimonial-card__avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #0088cc;
}
.testimonial-card__avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.testimonial-card__info {
  text-align: left;
}
@media (max-width: 575px) {
  .testimonial-card__info {
    text-align: center;
  }
}
.testimonial-card__name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.25rem;
}
.testimonial-card__title {
  color: #777777;
  font-size: 0.875rem;
}
.testimonial-card__rating {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
  justify-content: center;
}
@media (max-width: 575px) {
  .testimonial-card__rating {
    justify-content: center;
  }
}
.testimonial-card__rating i {
  color: #ffc107;
  font-size: 1rem;
}

.testimonials__nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}
.testimonials__nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: #ffffff;
  border: 2px solid #e5e5e5;
  border-radius: 50%;
  color: #777777;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.testimonials__nav-btn:hover {
  background-color: #0088cc;
  border-color: #0088cc;
  color: #ffffff;
}
.testimonials__nav-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.testimonials__nav-btn.disabled:hover {
  background-color: #ffffff;
  border-color: #e5e5e5;
  color: #777777;
}
.testimonials__nav-btn i {
  font-size: 1.2rem;
}

.testimonials__dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1.5rem;
}
.testimonials__dots-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e5e5e5;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}
.testimonials__dots-dot:hover {
  background-color: #0088cc;
}
.testimonials__dots-dot.active {
  background-color: #0088cc;
}

.testimonial-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 3rem;
  padding: 3rem 0;
  border-top: 1px solid #e5e5e5;
}
.testimonial-stats__item {
  text-align: center;
  padding: 1.5rem;
}
.testimonial-stats__item-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0088cc;
  margin-bottom: 0.5rem;
  display: block;
}
.testimonial-stats__item-label {
  color: #777777;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.testimonial-compact {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 0.25rem;
  border-left: 4px solid #0088cc;
  margin-bottom: 1.5rem;
}
.testimonial-compact__quote {
  font-size: 0.875rem;
  line-height: 1.6;
  color: #777777;
  font-style: italic;
  margin-bottom: 1rem;
}
.testimonial-compact__author {
  font-weight: 600;
  color: #333333;
  font-size: 0.875rem;
}
.testimonial-compact__author::before {
  content: "— ";
  color: #0088cc;
}

.logo-grid, .insurance__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  align-items: center;
  margin-top: 3rem;
}
@media (max-width: 575px) {
  .logo-grid, .insurance__grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}
@media (min-width: 768px) {
  .logo-grid, .insurance__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 992px) {
  .logo-grid, .insurance__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (min-width: 1200px) {
  .logo-grid, .insurance__grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
.logo-grid--insurance, .insurance__grid {
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}
@media (max-width: 575px) {
  .logo-grid--insurance, .insurance__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 768px) {
  .logo-grid--insurance, .insurance__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 992px) {
  .logo-grid--insurance, .insurance__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (min-width: 1200px) {
  .logo-grid--insurance, .insurance__grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
.logo-grid--partners {
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}
@media (max-width: 575px) {
  .logo-grid--partners {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (min-width: 768px) {
  .logo-grid--partners {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .logo-grid--partners {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1200px) {
  .logo-grid--partners {
    grid-template-columns: repeat(4, 1fr);
  }
}

.logo-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.25rem;
  border: 1px solid #e5e5e5;
  transition: all 0.3s ease-in-out;
  height: 100px;
}
.logo-item:hover {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transform: translateY(-2px);
  border-color: #0088cc;
}
.logo-item img {
  max-width: 100%;
  max-height: 60px;
  width: auto;
  height: auto;
  object-fit: contain;
  filter: grayscale(100%) opacity(0.7);
  transition: all 0.3s ease-in-out;
}
.logo-item:hover img {
  filter: grayscale(0%) opacity(1);
}
.logo-item--insurance {
  height: 80px;
  background-color: #f8f9fa;
  border: none;
}
.logo-item--insurance img {
  max-height: 40px;
  filter: grayscale(100%) opacity(0.6);
}
.logo-item--insurance:hover {
  background-color: #ffffff;
}
.logo-item--insurance:hover img {
  filter: grayscale(0%) opacity(1);
}
.logo-item--partner {
  height: 120px;
}
.logo-item--partner img {
  max-height: 80px;
}

.logo-carousel {
  position: relative;
  overflow: hidden;
  margin-top: 3rem;
}
.logo-carousel__track {
  display: flex;
  animation: logoScroll 30s linear infinite;
  gap: 3rem;
}
.logo-carousel__track:hover {
  animation-play-state: paused;
}
.logo-carousel__item {
  flex-shrink: 0;
  width: 150px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 0.25rem;
  border: 1px solid #e5e5e5;
}
.logo-carousel__item img {
  max-width: 80%;
  max-height: 60%;
  object-fit: contain;
  filter: grayscale(100%) opacity(0.7);
  transition: all 0.3s ease-in-out;
}
.logo-carousel__item:hover img {
  filter: grayscale(0%) opacity(1);
}

@keyframes logoScroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}
.logo-section {
  background-color: #f8f9fa;
  padding: 4rem 0;
}
@media (max-width: 575px) {
  .logo-section {
    padding: 3rem 0;
  }
}
.logo-section__header {
  text-align: center;
  margin-bottom: 3rem;
}
.logo-section__header h2 {
  margin-bottom: 1rem;
}
.logo-section__header p {
  color: #777777;
  font-size: 1.125rem;
  max-width: 600px;
  margin: 0 auto;
}
.logo-section--insurance {
  background-color: #ffffff;
  padding: 3rem 0;
}
.logo-section--insurance .logo-section__header h2 {
  font-size: 1.5rem;
  color: #777777;
}
.logo-section--partners {
  background-color: #f8f9fa;
}

@media (max-width: 575px) {
  .logo-grid, .insurance__grid {
    gap: 0.5rem;
  }
  .logo-item {
    padding: 1rem;
    height: 80px;
  }
  .logo-item img {
    max-height: 40px;
  }
  .logo-item--insurance {
    height: 60px;
  }
  .logo-item--insurance img {
    max-height: 30px;
  }
  .logo-item--partner {
    height: 100px;
  }
  .logo-item--partner img {
    max-height: 60px;
  }
  .logo-carousel__item {
    width: 120px;
    height: 60px;
  }
}
.logo-text-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
.logo-text-item:hover {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  transform: translateY(-5px);
}
.logo-text-item:hover {
  transform: translateY(-5px);
}
.logo-text-item__logo {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 50%;
  margin-bottom: 1rem;
}
.logo-text-item__logo img {
  max-width: 60%;
  max-height: 60%;
  object-fit: contain;
}
.logo-text-item__name {
  font-weight: 700;
  color: #333333;
  margin-bottom: 0.25rem;
}
.logo-text-item__description {
  color: #777777;
  font-size: 0.875rem;
  line-height: 1.5;
}

/*# sourceMappingURL=main.css.map */
