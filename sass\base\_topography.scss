// Base typography and layout
body {
  font-family: $main-font;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $font-color;
  background-color: $light-color;
}

// Headings
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: $heading-font;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: $spacer-md;
  color: $dark-color;
}

h1 {
  font-size: $large-header;
}

h2 {
  font-size: $medium-header;
}

h3 {
  font-size: $small-header;
}

h4 {
  font-size: $tiny-header;
}

// Paragraphs
p {
  margin-bottom: $spacer-md;

  &:last-child {
    margin-bottom: 0;
  }
}

// Links
a {
  color: $secondary-color;
  text-decoration: none;
  transition: $transition-base;

  &:hover {
    color: $primary-color;
  }
}

// Container
.container {
  @include container;
}

// Section
.section {
  @include section-padding;
}
