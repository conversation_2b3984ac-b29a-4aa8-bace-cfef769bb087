// Hero section styles - Porto Medical
.hero {
  position: relative;
  height: 500px;
  overflow: hidden;
  @include flex-center;

  &__slider {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  &__slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;

    &.active {
      opacity: 1;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        45deg,
        rgba($primary-color, 0.8),
        rgba($secondary-color, 0.6)
      );
      z-index: 1;
    }
  }

  &__content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: $light-color;
    max-width: 800px;
    padding: 0 $spacer-md;

    @include mobile {
      max-width: 100%;
    }
  }

  &__subtitle {
    font-size: $font-size-lg;
    font-weight: 400;
    margin-bottom: $spacer-md;
    text-transform: uppercase;
    letter-spacing: 2px;
    opacity: 0.9;

    @include mobile {
      font-size: $font-size-base;
    }
  }

  &__title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: $spacer-md;
    line-height: 1.2;

    @include mobile {
      font-size: 2rem;
    }

    @include tablet {
      font-size: 2.2rem;
    }
  }

  &__description {
    font-size: $font-size-lg;
    margin-bottom: $spacer-xl;
    opacity: 0.9;
    line-height: 1.6;

    @include mobile {
      font-size: $font-size-base;
      margin-bottom: $spacer-lg;
    }
  }

  &__actions {
    display: flex;
    gap: $spacer-md;
    justify-content: center;
    flex-wrap: wrap;

    @include mobile {
      flex-direction: column;
      align-items: center;
    }
  }

  // Navigation arrows
  &__nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    background-color: rgba($light-color, 0.2);
    border: 2px solid rgba($light-color, 0.3);
    color: $light-color;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    @include flex-center;
    cursor: pointer;
    transition: $transition-base;

    &:hover {
      background-color: rgba($light-color, 0.3);
      border-color: rgba($light-color, 0.5);
    }

    &--prev {
      left: $spacer-lg;
    }

    &--next {
      right: $spacer-lg;
    }

    @include mobile {
      display: none;
    }
  }

  // Dots indicator
  &__dots {
    position: absolute;
    bottom: $spacer-xl;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: $spacer-sm;
    z-index: 3;
  }

  &__dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba($light-color, 0.4);
    border: 2px solid rgba($light-color, 0.6);
    cursor: pointer;
    transition: $transition-base;

    &.active {
      background-color: $light-color;
    }

    &:hover {
      background-color: rgba($light-color, 0.8);
    }
  }
}

// Medical info cards (floating cards on hero)
.hero-cards {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: $spacer-lg;
  z-index: 4;

  @include mobile {
    position: relative;
    bottom: auto;
    left: auto;
    transform: none;
    flex-direction: column;
    margin-top: $spacer-lg;
    padding: 0 $spacer-md;
  }

  @include tablet {
    gap: $spacer-md;
  }
}

.hero-card {
  background-color: $light-color;
  padding: $spacer-xl $spacer-lg;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-lg;
  text-align: center;
  min-width: 250px;
  transition: $transition-base;

  @include mobile {
    min-width: auto;
    padding: $spacer-lg;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2);
  }

  &__icon {
    width: 60px;
    height: 60px;
    margin: 0 auto $spacer-md;
    @include flex-center;
    background-color: $secondary-color;
    border-radius: 50%;
    color: $light-color;
    font-size: 1.5rem;
  }

  &__title {
    font-size: $small-header;
    color: $dark-color;
    margin-bottom: $spacer-sm;
    font-weight: 700;
  }

  &__subtitle {
    color: $font-color;
    font-size: $font-size-sm;
    margin-bottom: $spacer-md;
  }

  &__info {
    font-size: $font-size-sm;
    color: $font-color;
    line-height: 1.5;

    strong {
      color: $dark-color;
      display: block;
      margin-bottom: $spacer-xs;
    }
  }
}
