// Department section layout
.department {
  background-color: $gray-color;
  @include section-padding;

  &__header {
    text-align: center;
    margin-bottom: $spacer-xl;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: $spacer-xl;
    margin-bottom: $spacer-xl;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacer-lg;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__actions {
    text-align: center;
    margin-top: $spacer-xl;
  }
}

// Legacy support (keeping old class name for backward compatibility)
.depratment {
  @extend .department;

  &__card-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacer-xl;
    margin: $spacer-xl 0;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacer-lg;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }

    .card-icon {
      width: 100%;
    }
  }

  &__btn {
    display: flex;
    justify-content: center;
    margin-top: $spacer-xl;
  }
}

// About section
.about {
  @include section-padding;
  background-color: $light-color;

  &__container {
    @include container;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacer-xxl;
    align-items: center;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacer-xl;
    }
  }

  &__content {
    h2 {
      margin-bottom: $spacer-lg;
    }

    p {
      margin-bottom: $spacer-md;
      font-size: $font-size-lg;
      line-height: 1.8;
    }

    .btn {
      margin-top: $spacer-lg;
    }
  }

  &__image {
    position: relative;
    border-radius: $border-radius-lg;
    overflow: hidden;
    box-shadow: $box-shadow-lg;

    img {
      width: 100%;
      height: auto;
      display: block;
    }

    &::after {
      content: "";
      position: absolute;
      top: 20px;
      right: 20px;
      bottom: 20px;
      left: 20px;
      border: 3px solid $secondary-color;
      border-radius: $border-radius-lg;
      pointer-events: none;
    }
  }
}

// Doctors section
.doctors {
  @include section-padding;
  background-color: $gray-light;

  &__header {
    text-align: center;
    margin-bottom: $spacer-xl;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  &__grid {
    @extend .doctors-grid;
  }

  &__actions {
    text-align: center;
    margin-top: $spacer-xl;
  }
}

// Resources section
.resources {
  @include section-padding;
  background-color: $light-color;

  &__header {
    text-align: center;
    margin-bottom: $spacer-xl;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  &__grid {
    @extend .resources-grid;
  }

  &__actions {
    text-align: center;
    margin-top: $spacer-xl;
  }
}

// Insurance section
.insurance {
  @include section-padding;
  background-color: $gray-light;

  &__header {
    text-align: center;
    margin-bottom: $spacer-xl;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  &__grid {
    @extend .logo-grid;
    @extend .logo-grid--insurance;
  }
}
