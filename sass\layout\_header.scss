// Header styles - Porto Medical
.header {
  background-color: $light-color;
  border-bottom: 1px solid $border-color;
  position: sticky;
  top: 0;
  z-index: 1000;

  // Main navigation only
  &__main {
    padding: 15px 0;

    .container {
      @include flex-between;
      align-items: center;
    }
  }

  &__logo {
    img {
      max-height: 50px;
      width: auto;
    }
  }

  &__nav {
    display: flex;
    align-items: center;

    @include mobile {
      display: none;
    }

    &-list {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 30px;
    }

    &-item {
      position: relative;

      &:hover .header__nav-dropdown {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }
    }

    &-link {
      color: $dark-color;
      font-weight: 400;
      font-size: 14px;
      padding: 10px 0;
      transition: $transition-base;
      text-decoration: none;

      &:hover {
        color: $secondary-color;
      }

      &--active {
        color: $secondary-color;
      }
    }

    &-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      background-color: $light-color;
      box-shadow: $box-shadow-lg;
      border-radius: $border-radius;
      padding: $spacer-md 0;
      min-width: 200px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: $transition-base;
      z-index: 1000;

      &-item {
        padding: $spacer-sm $spacer-md;

        &:hover {
          background-color: $gray-light;
        }
      }

      &-link {
        color: $dark-color;
        font-weight: 400;
        text-transform: none;
        letter-spacing: normal;

        &:hover {
          color: $secondary-color;
        }
      }
    }
  }

  // Mobile menu toggle
  &__toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: $dark-color;
    cursor: pointer;

    @include mobile {
      display: block;
    }
  }

  // Mobile menu
  &__mobile-menu {
    display: none;
    background-color: $light-color;
    border-top: 1px solid $border-color;
    padding: $spacer-md 0;

    @include mobile {
      &.active {
        display: block;
      }
    }

    .header__nav-list {
      flex-direction: column;
      gap: 0;
    }

    .header__nav-item {
      border-bottom: 1px solid $border-color;

      &:last-child {
        border-bottom: none;
      }
    }

    .header__nav-link {
      display: block;
      padding: $spacer-md;
      text-transform: none;
    }
  }
}
